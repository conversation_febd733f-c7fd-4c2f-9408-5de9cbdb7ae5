<template>
  <div class="export-tools-container" :class="{ 'export-tools-navbar': props.inNavbar }">
    <div class="export-tools-toggle" @click.stop="toggleDropdown">
      <span>导出数据</span>
      <div class="triangle" :class="{ 'triangle-up': showDropdown }"></div>
    </div>
    <div class="export-tools-dropdown" v-if="showDropdown">
      <!-- 导出格式选择 -->
      <div class="export-section">
        <div class="export-section-title">导出格式</div>
        <div
          v-for="format in exportFormats"
          :key="format.key"
          class="export-item"
          @click="exportData(format.key)"
        >
          <span class="export-icon" v-html="format.icon"></span>
          {{ format.label }}
        </div>
      </div>

      <div class="export-divider"></div>

      <!-- 数据统计 -->
      <div class="export-stats">
        <div class="stats-title">数据统计</div>
        <div class="stats-item">
          <span>绘制点：{{ dataStats.points }} 个</span>
        </div>
        <div class="stats-item">
          <span>绘制线：{{ dataStats.lines }} 条</span>
        </div>
        <div class="stats-item">
          <span>绘制面：{{ dataStats.polygons }} 个</span>
        </div>
        <div class="stats-item">
          <span>测量数据：{{ dataStats.measurements }} 项</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { GeoJSON, KML } from 'ol/format.js'
import { transform } from 'ol/proj.js'

const props = defineProps({
  map: Object,
  inNavbar: { type: Boolean, default: false }
})

const showDropdown = ref(false)

// 导出格式配置
const exportFormats = [
  {
    key: 'geojson',
    label: 'GeoJSON',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"></path>
    </svg>`
  },
  {
    key: 'kml',
    label: 'KML',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z"></path>
    </svg>`
  },
  {
    key: 'csv',
    label: 'CSV',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"></path>
    </svg>`
  },
  {
    key: 'json',
    label: 'JSON',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M5,3H7V5H5V10A2,2 0 0,1 3,12A2,2 0 0,1 5,14V19H7V21H5C3.93,20.73 3,20.1 3,19V15A2,2 0 0,0 1,13H0V11H1A2,2 0 0,0 3,9V5C3,3.9 3.9,3 5,3M19,3A2,2 0 0,1 21,5V9A2,2 0 0,0 23,11H24V13H23A2,2 0 0,0 21,15V19A2,2 0 0,1 19,21H17V19H19V14A2,2 0 0,1 21,12A2,2 0 0,1 19,10V5H17V3H19Z"></path>
    </svg>`
  }
]

// 数据统计
const dataStats = computed(() => {
  if (!props.map) {
    return { points: 0, lines: 0, polygons: 0, measurements: 0 }
  }

  let points = 0
  let lines = 0
  let polygons = 0
  let measurements = 0

  try {
    // 遍历地图图层收集数据
    const layers = props.map.getLayers().getArray()
    layers.forEach(layer => {
      // 只统计矢量图层，排除底图图层
      if (layer.getSource && layer.getSource().getFeatures &&
          !['tian', 'tianlabel', 'baidu', 'gaode'].includes(layer.get('name'))) {
        const features = layer.getSource().getFeatures()
        features.forEach(feature => {
          const geometry = feature.getGeometry()
          if (geometry) {
            const type = geometry.getType()
            switch (type) {
              case 'Point':
                points++
                break
              case 'LineString':
                lines++
                break
              case 'Polygon':
                polygons++
                break
            }
          }
        })
      }
    })

    // 统计测量数据（通过覆盖物）
    const overlays = props.map.getOverlays().getArray()
    overlays.forEach(overlay => {
      const element = overlay.getElement()
      if (element && element.className &&
          (element.className.includes('ol-tooltip-static') ||
           element.className.includes('ol-tooltip-result'))) {
        measurements++
      }
    })
  } catch (error) {
    console.warn('统计数据时出错:', error)
  }

  return { points, lines, polygons, measurements }
})

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// 收集所有绘制数据
const collectDrawingData = () => {
  if (!props.map) return { features: [], measurements: [] }

  const features = []
  const measurements = []

  try {
    // 收集矢量要素
    const layers = props.map.getLayers().getArray()
    layers.forEach(layer => {
      // 只处理矢量图层，排除底图图层
      if (layer.getSource && layer.getSource().getFeatures &&
          !['tian', 'tianlabel', 'baidu', 'gaode'].includes(layer.get('name'))) {
        const layerFeatures = layer.getSource().getFeatures()
        layerFeatures.forEach(feature => {
          const geometry = feature.getGeometry()
          if (geometry) {
            try {
              // 转换坐标系到WGS84
              const clonedFeature = feature.clone()
              const clonedGeometry = clonedFeature.getGeometry()
              clonedGeometry.transform('EPSG:3857', 'EPSG:4326')

              // 获取要素属性
              const properties = feature.getProperties()
              delete properties.geometry // 移除几何属性避免重复

              features.push({
                type: 'Feature',
                properties: {
                  name: feature.get('name') || `${geometry.getType()}_${features.length + 1}`,
                  geometryType: geometry.getType(),
                  layerName: layer.get('name') || 'unknown',
                  ...properties
                },
                geometry: new GeoJSON().writeGeometryObject(clonedGeometry)
              })
            } catch (error) {
              console.warn('处理要素时出错:', error)
            }
          }
        })
      }
    })

    // 收集测量数据
    const overlays = props.map.getOverlays().getArray()
    overlays.forEach(overlay => {
      const element = overlay.getElement()
      const position = overlay.getPosition()

      if (element && element.className && position &&
          (element.className.includes('ol-tooltip-static') ||
           element.className.includes('ol-tooltip-result'))) {

        try {
          // 转换坐标
          const wgs84Position = transform(position, 'EPSG:3857', 'EPSG:4326')

          // 提取测量内容
          let content = element.innerHTML.replace(/<[^>]*>/g, '') // 移除HTML标签
          content = content.replace(/\s+/g, ' ').trim() // 清理空白字符

          if (content) {
            measurements.push({
              type: 'measurement',
              content: content,
              position: wgs84Position,
              timestamp: new Date().toISOString()
            })
          }
        } catch (error) {
          console.warn('处理测量数据时出错:', error)
        }
      }
    })
  } catch (error) {
    console.error('收集数据时出错:', error)
  }

  return { features, measurements }
}

// 导出数据
const exportData = (format) => {
  try {
    const data = collectDrawingData()

    if (data.features.length === 0 && data.measurements.length === 0) {
      alert('没有可导出的数据！\n\n请先在地图上绘制一些图形或进行测量，然后再尝试导出。')
      return
    }

    let content = ''
    let filename = ''
    let mimeType = ''
    const dateStr = new Date().toISOString().split('T')[0]

    switch (format) {
      case 'geojson':
        content = JSON.stringify({
          type: 'FeatureCollection',
          features: data.features,
          measurements: data.measurements,
          exportInfo: {
            date: new Date().toISOString(),
            totalFeatures: data.features.length,
            totalMeasurements: data.measurements.length
          }
        }, null, 2)
        filename = `地图数据_${dateStr}.geojson`
        mimeType = 'application/geo+json'
        break

      case 'kml':
        content = generateKML(data)
        filename = `地图数据_${dateStr}.kml`
        mimeType = 'application/vnd.google-earth.kml+xml'
        break

      case 'csv':
        content = generateCSV(data)
        filename = `地图数据_${dateStr}.csv`
        mimeType = 'text/csv'
        break

      case 'json':
        content = JSON.stringify({
          exportInfo: {
            date: new Date().toISOString(),
            format: 'JSON',
            application: 'WebGIS地图系统'
          },
          statistics: dataStats.value,
          features: data.features,
          measurements: data.measurements
        }, null, 2)
        filename = `地图数据_${dateStr}.json`
        mimeType = 'application/json'
        break

      default:
        alert('不支持的导出格式')
        return
    }

    if (content) {
      downloadFile(content, filename, mimeType)
      showDropdown.value = false

      // 显示成功消息
      setTimeout(() => {
        console.log(`✅ 成功导出 ${data.features.length} 个图形和 ${data.measurements.length} 个测量数据`)
      }, 100)
    }
  } catch (error) {
    console.error('导出数据时出错:', error)
    alert('导出失败，请检查控制台了解详细错误信息')
  }
}

// 生成KML格式
const generateKML = (data) => {
  let kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>地图绘制数据</name>
    <description>导出时间: ${new Date().toLocaleString()}</description>
`

  // 添加绘制要素
  data.features.forEach((feature, index) => {
    const geometry = feature.geometry
    const props = feature.properties

    kml += `    <Placemark>
      <name>${props.name || `要素${index + 1}`}</name>
      <description>类型: ${props.type}</description>
`

    if (geometry.type === 'Point') {
      kml += `      <Point>
        <coordinates>${geometry.coordinates[0]},${geometry.coordinates[1]}</coordinates>
      </Point>
`
    } else if (geometry.type === 'LineString') {
      const coords = geometry.coordinates.map(coord => `${coord[0]},${coord[1]}`).join(' ')
      kml += `      <LineString>
        <coordinates>${coords}</coordinates>
      </LineString>
`
    } else if (geometry.type === 'Polygon') {
      const coords = geometry.coordinates[0].map(coord => `${coord[0]},${coord[1]}`).join(' ')
      kml += `      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>${coords}</coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
`
    }

    kml += `    </Placemark>
`
  })

  // 添加测量数据
  data.measurements.forEach((measurement, index) => {
    kml += `    <Placemark>
      <name>测量${index + 1}</name>
      <description>${measurement.content}</description>
      <Point>
        <coordinates>${measurement.position[0]},${measurement.position[1]}</coordinates>
      </Point>
    </Placemark>
`
  })

  kml += `  </Document>
</kml>`

  return kml
}

// 生成CSV格式
const generateCSV = (data) => {
  let csv = '类型,名称,几何类型,经度,纬度,内容,图层,时间戳\n'

  // 添加绘制要素
  data.features.forEach((feature, index) => {
    const geometry = feature.geometry
    const props = feature.properties

    let longitude = ''
    let latitude = ''
    let content = ''

    if (geometry.type === 'Point') {
      longitude = geometry.coordinates[0].toFixed(6)
      latitude = geometry.coordinates[1].toFixed(6)
      content = `点坐标: ${longitude}, ${latitude}`
    } else if (geometry.type === 'LineString') {
      const firstPoint = geometry.coordinates[0]
      longitude = firstPoint[0].toFixed(6)
      latitude = firstPoint[1].toFixed(6)
      content = `线段起点，共${geometry.coordinates.length}个点`
    } else if (geometry.type === 'Polygon') {
      const firstPoint = geometry.coordinates[0][0]
      longitude = firstPoint[0].toFixed(6)
      latitude = firstPoint[1].toFixed(6)
      content = `多边形，共${geometry.coordinates[0].length}个顶点`
    }

    const name = props.name || `${geometry.type}_${index + 1}`
    const layerName = props.layerName || '未知图层'
    const timestamp = new Date().toISOString()

    csv += `绘制要素,"${name}",${geometry.type},${longitude},${latitude},"${content}","${layerName}",${timestamp}\n`
  })

  // 添加测量数据
  data.measurements.forEach((measurement, index) => {
    const longitude = measurement.position[0].toFixed(6)
    const latitude = measurement.position[1].toFixed(6)
    const content = measurement.content.replace(/"/g, '""') // 转义双引号
    const name = `测量${index + 1}`

    csv += `测量数据,"${name}",Point,${longitude},${latitude},"${content}","测量图层",${measurement.timestamp}\n`
  })

  return csv
}

// 下载文件
const downloadFile = (content, filename, mimeType) => {
  try {
    const blob = new Blob([content], { type: mimeType + ';charset=utf-8' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => {
      URL.revokeObjectURL(url)
    }, 100)

    // 显示成功提示
    console.log(`✅ 文件已成功下载: ${filename}`)
    console.log(`📁 文件大小: ${(blob.size / 1024).toFixed(2)} KB`)

  } catch (error) {
    console.error('下载文件时出错:', error)
    alert('文件下载失败，请重试')
  }
}

// 点击外部关闭下拉菜单
function handleClickOutside(event) {
  const exportTools = document.querySelector('.export-tools-container')
  if (exportTools && !exportTools.contains(event.target)) {
    showDropdown.value = false
  }
}

// 添加和移除点击事件监听器
onMounted(() => {
  if (props.inNavbar) {
    showDropdown.value = true
  } else {
    document.addEventListener('click', handleClickOutside)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style>
.export-tools-container {
  position: absolute;
  top: 10px;
  left: 440px;
  z-index: 4000;
  font-size: 15px;
  color: #333;
  min-width: 120px;
}

.export-tools-toggle {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  user-select: none;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  transition: background 0.2s;
}

.export-tools-toggle:hover {
  background-color: #f0f9ff;
}

.triangle {
  margin-left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 6px solid #1890ff;
  transition: transform 0.2s;
}

.triangle-up {
  transform: rotate(180deg);
}

.export-tools-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  margin-top: 8px;
  padding: 0;
  overflow: hidden;
  border: none;
  min-width: 200px;
}

.export-section {
  padding: 12px 0;
}

.export-section-title {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.export-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
  background: #fff;
  font-size: 15px;
}

.export-item:hover {
  background-color: #f5f7fa;
  color: #1890ff;
}

.export-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.export-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 8px 0;
}

.export-stats {
  padding: 12px 16px;
  background-color: #f8f9fa;
}

.stats-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.stats-item {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.export-tools-navbar {
  position: relative;
  top: 0;
  left: 0;
}

.export-tools-navbar .export-tools-toggle {
  display: none;
}

.export-tools-navbar .export-tools-dropdown {
  position: relative;
  margin-top: 0;
  box-shadow: none;
  border: none;
}
</style>
