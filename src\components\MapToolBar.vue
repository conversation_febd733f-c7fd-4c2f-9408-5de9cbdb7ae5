<template>
  <div class="map-toolbar">
    <div class="toolbar-group">
      <button
        v-for="btn in buttons"
        :key="btn.key"
        class="toolbar-btn"
        :class="{ active: btn.active }"
        :ref="el => btnRefs[btn.key] = el"
        @click="onBtnClick(btn.key)"
        :title="btn.label"
      >
        <span class="toolbar-icon" v-html="btn.icon"></span>
        <span class="toolbar-text">{{ btn.label }}</span>
      </button>
    </div>
    <!-- 路线规划跳转按钮 -->
    <button class="toolbar-btn route-btn" @click="goRoute" title="路线规划">
      <span class="toolbar-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#409EFF">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17.93V19c0-.55-.45-1-1-1s-1 .45-1 1v.93C7.05 18.44 4 15.03 4 11c0-1.85.63-3.55 1.69-4.9l1.43 1.43C6.42 8.36 6 9.63 6 11c0 3.31 2.69 6 6 6s6-2.69 6-6c0-1.37-.42-2.64-1.12-3.67l1.43-1.43C19.37 7.45 20 9.15 20 11c0 4.03-3.05 7.44-7 8.93z"/>
        </svg>
      </span>
      <span class="toolbar-text">路线规划</span>
    </button>



    <!-- 世界遗产搜索按钮 -->
    <button class="toolbar-btn heritage-btn" @click="toggleHeritageSearch" title="世界遗产搜索" ref="heritageSearchBtn">
      <span class="toolbar-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#409EFF">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-1.6-6.5-5.43-6.5-9.93 0-1.18.22-2.29.6-3.34l4.9 4.9v.44c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V10h2c.83 0 1.5-.67 1.5-1.5S14.33 7 13.5 7h-2V5.5c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.9L4.13 2.03c-.09-.07-.21-.03-.26.07-.07.15-.01.32.12.41L8.97 7c-.62.85-1 1.9-1 3 0 2.8 2.2 5.1 5 5.1.8 0 1.55-.2 2.21-.52l2.55 2.55c.07.07.17.1.26.1s.19-.03.26-.1c.15-.15.15-.39 0-.54L15.5 13.84c.32-.67.5-1.42.5-2.2 0-2.8-2.2-5.1-5-5.1-.8 0-1.55.2-2.21.52l-2.55-2.55c-.15-.15-.4-.15-.55 0s-.15.4 0 .55L8.5 7.29C8.18 7.95 8 8.7 8 9.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-.44L6.1 4.16c.98-.63 2.13-1 3.36-1 3.4 0 6.15 2.75 6.15 6.15 0 1.23-.37 2.38-1 3.36l-3.9-3.9v.44c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5V7.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V9h2c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-2v1.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5v-.44l4.9 4.9c-1.05.38-2.16.6-3.34.6-1.23 0-2.38-.37-3.36-1l3.91-3.91v.44c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V10h-2c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5h2V5.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v.9l4.38-4.38c.14-.14.14-.36 0-.5s-.36-.14-.5 0L13.84 5.5c-.67-.32-1.42-.5-2.2-.5-1.95 0-3.6 1.4-3.94 3.26L11 11.58v.92c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5v-.58L4.5 8.42c0 3.9 2.55 7.2 6.5 8.58v-3.93c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5v-1.93z"/>
        </svg>
      </span>
      <span class="toolbar-text">世界遗产</span>
    </button>
  </div>
</template>

<script setup>
import { computed, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  active: Object 
})

const btnRefs = reactive({})
const heritageSearchBtn = ref(null)
const router = useRouter()

const buttons = computed(() => [
  {
    key: 'layer',
    label: '图层',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M11.99 18.54l-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16"></path>
    </svg>`,
    active: props.active?.layer
  },
  {
    key: 'map',
    label: '操作',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .*********.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"></path>
    </svg>`,
    active: props.active?.map
  },
  {
    key: 'draw',
    label: '绘制',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"></path>
    </svg>`,
    active: props.active?.draw
  },
  {
    key: 'measure',
    label: '测量',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
    </svg>`,
    active: props.active?.measure
  },
  {
    key: 'locate',
    label: '定位',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3A8.994 8.994 0 0 0 13 3.06V1h-2v2.06A8.994 8.994 0 0 0 3.06 11H1v2h2.06A8.994 8.994 0 0 0 11 20.94V23h2v-2.06A8.994 8.994 0 0 0 20.94 13H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"></path>
    </svg>`,
    active: props.active?.locate
  },
])

const emit = defineEmits(['toggle', 'toggle-heritage-search'])

function onBtnClick(key) {
  // 计算按钮位置
  const el = btnRefs[key]
  if (el) {
    const rect = el.getBoundingClientRect()
    emit('toggle', { key, rect })
  } else {
    emit('toggle', { key })
  }
}

function goRoute() {
  router.push('/gaoderoute')
}



function toggleHeritageSearch() {
  // 获取按钮位置
  if (heritageSearchBtn.value) {
    const rect = heritageSearchBtn.value.getBoundingClientRect()
    emit('toggle-heritage-search', { rect })
  } else {
    emit('toggle-heritage-search', {})
  }
}
</script>

<style scoped>
.map-toolbar {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  flex-direction: column;
  z-index: 10010;
}

.toolbar-group {
  display: flex;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  padding: 2px;
}

.toolbar-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: none;
  padding: 0 16px;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  border-right: 1px solid #f0f0f0;
  height: 36px;
  min-width: 70px;
  margin: 0;
  letter-spacing: 0.5px;
}

.toolbar-btn:last-child {
  border-right: none;
}

.toolbar-btn:hover {
  background: #f9f9f9;
  color: #1890ff;
}

.toolbar-btn.active {
  background: #f0f0f0;
  color: #1890ff;
}

.toolbar-btn.route-btn {
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
  background: #f6faff;
  color: #409EFF;
  font-weight: 600;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}

.toolbar-btn.route-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.toolbar-btn.province-btn, .toolbar-btn.heritage-btn {
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
  background: #f6faff;
  color: #409EFF;
  font-weight: 600;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}

.toolbar-btn.province-btn:hover, .toolbar-btn.heritage-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.toolbar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: #1890ff;
  font-size: 16px;
}

.toolbar-text {
  font-size: 14px;
  font-weight: normal;
  color: #333;
}
</style>
