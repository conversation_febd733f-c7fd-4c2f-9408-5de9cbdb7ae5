<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Gauging from './Gauging.vue'

const props = defineProps({
  map: Object,
  inNavbar: { type: Boolean, default: false }
})


const showDropdown = ref(false)
const currentTool = ref(null)
const gaugingRef = ref(null)

// 工具列表
const tools = [
  {
    key: 'distance',
    label: '测量距离',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
    </svg>`,
    ref: gaugingRef,
    startMethod: 'startMeasure',
    param: 'distance'
  },
  {
    key: 'area',
    label: '测量面积',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M17.66 17.66l-1.06 1.06-2.83-2.83 1.06-1.06L14 14l-1.94-1.94-1.06 1.06-2.83-2.83 1.06-1.06L8 8 5.17 5.17 4.11 6.23 1.28 3.4 2.34 2.34l18.38 18.38-1.06 1.06-2.83-2.83 1.06-1.06L17.66 17.66zM7 17h10v-4.68l-10-10V17z"></path>
    </svg>`,
    ref: gaugingRef,
    startMethod: 'startMeasure',
    param: 'area'
  }
]

// 清除工具
const clearTool = {
  key: 'clear',
  label: '清除测量',
  icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"></path>
  </svg>`,
  ref: gaugingRef,
  method: 'clearMeasurement'
}

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value

}

// 在导航栏中使用时，默认显示下拉菜单
onMounted(() => {
  if (props.inNavbar) {
    showDropdown.value = true
  }
})

// 选择工具
const selectTool = (tool) => {
  if (currentTool.value === tool.key) {
    stopCurrentTool()
    currentTool.value = null
    return
  }

  stopCurrentTool()

  currentTool.value = tool.key

  // 启动新工具
  if (tool.ref.value && tool.startMethod) {
    if (tool.param) {
      tool.ref.value[tool.startMethod](tool.param)
    } else {
      tool.ref.value[tool.startMethod]()
    }
  }

}

// 执行清除操作
const executeClear = () => {
  if (clearTool.ref.value && clearTool.method) {
    clearTool.ref.value[clearTool.method]()
  }

}

const stopCurrentTool = () => {
  if (!currentTool.value) return
  const tool = tools.find(t => t.key === currentTool.value)
  if (tool && tool.ref.value) {
    // 只停止测量交互，不清除已测量的数据
    tool.ref.value.stopMeasure()
  }
}

// 点击外部关闭下拉菜单
function handleClickOutside(event) {
  const measureTools = document.querySelector('.measure-tools-container')
  if (measureTools && !measureTools.contains(event.target)) {
    showDropdown.value = false
  }
}

// 添加和移除点击事件监听器
onMounted(() => {
  if (props.inNavbar) {
    showDropdown.value = true
  } else {
    document.addEventListener('click', handleClickOutside)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="measure-tools-container" :class="{ 'measure-tools-navbar': props.inNavbar }">
    <div class="measure-tools-toggle" @click.stop="toggleDropdown">
      <span>测量工具</span>
      <div class="triangle" :class="{ 'triangle-up': showDropdown }"></div>
    </div>
    <div class="measure-tools-dropdown" v-if="showDropdown">
      <!-- 测量工具部分 -->
      <div
        v-for="tool in tools"
        :key="tool.key"
        class="measure-tools-item"
        :class="{ active: currentTool === tool.key }"
        @click="selectTool(tool)"
      >
        <span class="tool-icon" v-html="tool.icon"></span>
        {{ tool.label }}
      </div>

      <div class="measure-tools-divider"></div>

      <!-- 清除按钮 -->
      <div
        class="measure-tools-item clear-item"
        @click="executeClear"
      >
        <span class="tool-icon" v-html="clearTool.icon"></span>
        {{ clearTool.label }}
      </div>
    </div>

    <!-- 引入测量组件 -->
    <Gauging :map="map" v-if="map" ref="gaugingRef" class="hidden-tool" />
  </div>
</template>

<style>
.measure-tools-container {
  position: absolute;
  top: 10px;
  left: 320px;
  z-index: 4000;
  font-size: 15px;
  color: #333;
  min-width: 120px;
}

.measure-tools-toggle {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  user-select: none;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  transition: background 0.2s;
}

.measure-tools-toggle:hover {
  background-color: #f5f7fa;
}

.triangle {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #1890ff;
  transition: transform 0.2s ease;
}

.triangle-up {
  transform: rotate(180deg);
}

.measure-tools-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 180px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 4px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  overflow: hidden;
  z-index: 5000;
  border: none;
}

.measure-tools-item {
  padding: 10px 18px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  display: flex;
  align-items: center;
  font-size: 15px;
  border-radius: 0;
}

.measure-tools-item .tool-icon {
  margin-right: 10px;
  font-size: 18px;
}

.measure-tools-item:hover,
.measure-tools-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.clear-item {
  color: #ff4d4f;
}

.clear-item:hover {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.measure-tools-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 6px 0;
}

.hidden-tool {
  display: none;
}

/* 导航栏中的测量工具样式 */
.measure-tools-navbar {
  position: static !important;
}

.measure-tools-navbar .measure-tools-toggle {
  display: none !important;
}

.measure-tools-navbar .measure-tools-dropdown {
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: auto !important;
  right: 0 !important;
  border: 1px solid #e5e6eb !important;
  background-color: #fff !important;
  color: #222 !important;
  box-shadow: 0 6px 24px rgba(0,0,0,0.08) !important;
  border-radius: 10px !important;
}

.measure-tools-navbar .dropdown-header {
  background-color: #fff !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.measure-tools-navbar .dropdown-title {
  color: #222 !important;
}

.measure-tools-navbar .measure-tools-item {
  color: #222 !important;
  background: #fff !important;
  font-size: 15px !important;
}

.measure-tools-navbar .measure-tools-item:hover {
  background-color: #f5f7fa !important;
  color: #1890ff !important;
}

.measure-tools-navbar .measure-tools-item.active {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.measure-tools-navbar .measure-tools-divider {
  background-color: #f0f0f0 !important;
}

.measure-tools-navbar .measure-tools-item.clear-item:hover {
  background-color: #fff1f0 !important;
  color: #ff4d4f !important;
}
</style>
