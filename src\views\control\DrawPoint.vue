<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Draw from 'ol/interaction/Draw.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer.js'
import { Style, Circle as CircleStyle, Fill, Stroke, Text } from 'ol/style.js'
import Overlay from 'ol/Overlay.js'
import { Point } from 'ol/geom.js'
import Feature from 'ol/Feature.js'
import { transform } from 'ol/proj.js'
import { toStringHDMS } from 'ol/coordinate.js'

const props = defineProps({
  map: Object
})

const drawing = ref(false)
const source = ref(null)
const vector = ref(null)
const draw = ref(null)
const pointCounter = ref(0)
const tooltipElement = ref(null)
const tooltip = ref(null)

const initDraw = () => {
  if (!props.map) return

  source.value = new VectorSource()
  vector.value = new VectorLayer({
    source: source.value,
    style: (feature) => {
      const pointName = feature.get('name') || ''

      return new Style({
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: 'rgba(255, 0, 0, 0.7)'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 2
          })
        }),
        text: new Text({
          text: pointName,
          offsetY: -15,
          font: '14px sans-serif',
          fill: new Fill({
            color: '#333'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 3
          })
        })
      })
    },
    zIndex: 1000
  })

  props.map.addLayer(vector.value)

  createTooltip()
}

// 创建提示框
const createTooltip = () => {
  if (tooltipElement.value) {
    tooltipElement.value.parentNode.removeChild(tooltipElement.value)
  }

  tooltipElement.value = document.createElement('div')
  tooltipElement.value.className = 'ol-tooltip'
  tooltip.value = new Overlay({
    element: tooltipElement.value,
    offset: [15, 0],
    positioning: 'center-left'
  })

  props.map.addOverlay(tooltip.value)
}

const formatCoordinate = (coordinate) => {
  return toStringHDMS(transform(coordinate, 'EPSG:3857', 'EPSG:4326'))
}

// 开始绘制点
const startDrawPoint = () => {
  if (!props.map) return

  if (drawing.value) {
    stopDrawing()
  }

  drawing.value = true

  // 创建绘图交互
  draw.value = new Draw({
    source: source.value,
    type: 'Point',
    style: new Style({
      image: new CircleStyle({
        radius: 7,
        fill: new Fill({
          color: 'rgba(255, 0, 0, 0.7)'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        })
      })
    })
  })

  props.map.addInteraction(draw.value)

  // 添加绘图结束事件
  draw.value.on('drawend', (evt) => {
    const feature = evt.feature
    const geometry = feature.getGeometry()
    const coord = geometry.getCoordinates()

    pointCounter.value++

    feature.set('name', `点 ${pointCounter.value}`)

    const hdms = formatCoordinate(coord)

    // 显示坐标信息
    const pointElement = document.createElement('div')
    pointElement.className = 'ol-tooltip ol-tooltip-static'
    pointElement.innerHTML = `<div>点 ${pointCounter.value}</div><div>${hdms}</div>`

    const pointTooltip = new Overlay({
      element: pointElement,
      offset: [0, -15],
      positioning: 'bottom-center',
      stopEvent: false
    })

    pointTooltip.setPosition(coord)
    props.map.addOverlay(pointTooltip)

    console.log(`点 ${pointCounter.value}: ${hdms}`)
  })

  // 添加鼠标移动事件
  const pointerMoveHandler = (evt) => {
    if (evt.dragging || !drawing.value) {
      return
    }

    const helpMsg = '点击地图添加点'

    if (tooltipElement.value) {
      tooltipElement.value.innerHTML = helpMsg
      tooltip.value.setPosition(evt.coordinate)
    }
  }

  props.map.on('pointermove', pointerMoveHandler)
}

// 停止绘制（但保留已绘制的点）
const stopDrawing = () => {
  if (!props.map) return

  drawing.value = false

  // 移除绘图交互
  if (draw.value) {
    props.map.removeInteraction(draw.value)
    draw.value = null
  }

  // 移除动态提示框（但保留静态标签）
  if (tooltip.value) {
    props.map.removeOverlay(tooltip.value)
    tooltip.value = null
  }

  if (tooltipElement.value) {
    if (tooltipElement.value.parentNode) {
      tooltipElement.value.parentNode.removeChild(tooltipElement.value)
    }
    tooltipElement.value = null
  }

  // 注意：不清除 source 中的要素，保留已绘制的点
}

// 清除
const clearPoints = () => {
  if (!props.map) return
  stopDrawing()
  if (source.value) {
    source.value.clear()
  }

  // 移除所有覆盖物
  const overlays = props.map.getOverlays().getArray().slice()
  overlays.forEach(overlay => {
    const element = overlay.getElement()
    if (element && element.className && element.className.includes('ol-tooltip-static')) {
      props.map.removeOverlay(overlay)
    }
  })

  // 重置
  pointCounter.value = 0

  createTooltip()

  // 显示清除成功的提示
  const successMsg = document.createElement('div')
  successMsg.className = 'ol-tooltip ol-tooltip-success'
  successMsg.innerHTML = '已清除所有点'

  const successTooltip = new Overlay({
    element: successMsg,
    offset: [0, 0],
    positioning: 'center-center',
    stopEvent: false
  })

  // 获取地图中心点
  const center = props.map.getView().getCenter()
  successTooltip.setPosition(center)
  props.map.addOverlay(successTooltip)

  // 2秒后自动移除提示
  setTimeout(() => {
    props.map.removeOverlay(successTooltip)
    if (successMsg.parentNode) {
      successMsg.parentNode.removeChild(successMsg)
    }
  }, 1500)
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initDraw()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  stopDrawing()

  if (props.map && vector.value) {
    props.map.removeLayer(vector.value)
  }
})

defineExpose({
  startDrawPoint,
  stopDrawing,
  clearPoints
})
</script>

<template>
</template>

<style>
.draw-tools {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.draw-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.draw-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 16px;
}

.draw-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.draw-button.active {
  background-color: #1890ff;
  color: white;
}

.draw-button.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.draw-button.clear-button:hover {
  background-color: #ff4d4f;
  color: white;
}

/* 提示框样式 */
.ol-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #333;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.ol-tooltip-static {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid #1890ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.ol-tooltip-success {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 1.5s ease-in-out;
  pointer-events: none;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.8); }
  20% { opacity: 1; transform: scale(1.1); }
  30% { opacity: 1; transform: scale(1); }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
</style>
