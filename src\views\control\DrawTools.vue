<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import DrawPoint from './DrawPoint.vue'
import DrawLine from './DrawLine.vue'
import DrawBezier from './DrawBezier.vue'
import DrawPolygon from './DrawPolygon.vue'

const props = defineProps({
  map: Object,
  inNavbar: { type: Boolean, default: false }
})

// 下拉菜单状态
const showDropdown = ref(false)
const currentTool = ref(null)
const drawPointRef = ref(null)
const drawLineRef = ref(null)
const drawBezierRef = ref(null)
const drawPolygonRef = ref(null)

// 工具列表
const tools = [
  {
    key: 'point',
    label: '绘制点',
    ref: drawPointRef,
    startMethod: 'startDrawPoint'
  },
  {
    key: 'line',
    label: '绘制线',
    ref: drawLineRef,
    startMethod: 'startDrawLine'
  },
  {
    key: 'bezier',
    label: '绘制曲线',
    ref: drawBezierRef,
    startMethod: 'startDrawBezier'
  },
  {
    key: 'polygon',
    label: '绘制多边形',
    ref: drawPolygonRef,
    startMethod: 'startDrawPolygon'
  }
]

// 清除工具列表
const clearTools = [
  {
    key: 'clearPoint',
    label: '清除点',
    ref: drawPointRef,
    clearMethod: 'clearPoints'
  },
  {
    key: 'clearLine',
    label: '清除线',
    ref: drawLineRef,
    clearMethod: 'clearLines'
  },
  {
    key: 'clearBezier',
    label: '清除曲线',
    ref: drawBezierRef,
    clearMethod: 'clearCurves'
  },
  {
    key: 'clearPolygon',
    label: '清除多边形',
    ref: drawPolygonRef,
    clearMethod: 'clearPolygons'
  },
  {
    key: 'clearAll',
    label: '清除所有',
    clearMethod: 'clearAll'
  }
]

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

onMounted(() => {
  if (props.inNavbar) {
    showDropdown.value = true
  }
})

// 选择工具
const selectTool = (tool) => {
  if (currentTool.value === tool.key) {
    stopCurrentTool()
    currentTool.value = null
    return
  }

  stopCurrentTool()

  currentTool.value = tool.key

  if (tool.ref.value && tool.startMethod) {
    tool.ref.value[tool.startMethod]()
  }

}

// 清除工具
const clearTool = (tool) => {
  if (tool.key === 'clearAll') {
    clearAll()
  } else if (tool.ref.value && tool.clearMethod) {
    tool.ref.value[tool.clearMethod]()
  }

}

// 停止当前工具（但不清除数据）
const stopCurrentTool = () => {
  if (!currentTool.value) return
  const tool = tools.find(t => t.key === currentTool.value)
  if (tool && tool.ref.value) {
    // 只停止绘制交互，不清除已绘制的图形
    tool.ref.value.stopDrawing()
  }
}

// 清除所有
const clearAll = () => {
  stopCurrentTool()
  currentTool.value = null

  clearTools.forEach(tool => {
    if (tool.key !== 'clearAll' && tool.ref.value && tool.clearMethod) {
      tool.ref.value[tool.clearMethod]()
    }
  })
}

// 点击外部关闭下拉菜单
function handleClickOutside(event) {
  const drawTools = document.querySelector('.draw-tools-container')
  if (drawTools && !drawTools.contains(event.target)) {
    showDropdown.value = false
  }
}

// 添加和移除点击事件监听器
onMounted(() => {
  if (props.inNavbar) {
    showDropdown.value = true
  } else {
    document.addEventListener('click', handleClickOutside)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="draw-tools-container" :class="{ 'draw-tools-navbar': props.inNavbar }">
    <div class="draw-tools-toggle" @click.stop="toggleDropdown">
      <div class="toggle-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"></path>
        </svg>
      </div>
      <span>绘图工具</span>
      <div class="triangle" :class="{ 'triangle-up': showDropdown }"></div>
    </div>
    <div class="draw-tools-dropdown" v-if="showDropdown">
      <!-- 绘图工具部分 -->
      <div class="draw-tools-section">
        <div class="draw-tools-section-title">绘图工具</div>
        <div
          v-for="tool in tools"
          :key="tool.key"
          class="draw-tools-item"
          :class="{ active: currentTool === tool.key }"
          @click="selectTool(tool)"
        >
          <div class="tool-icon">
            <svg v-if="tool.key === 'point'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"></path>
            </svg>
            <svg v-else-if="tool.key === 'line'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path>
            </svg>
            <svg v-else-if="tool.key === 'bezier'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M4.59 6.89c.7-.71 1.4-1.35 1.71-1.22.5.2 0 1.03-.3 1.52-.25.42-2.86 3.89-2.86 6.31 0 1.28.48 2.34 1.34 2.98.75.56 1.74.73 2.64.46 1.07-.31 1.95-1.4 3.06-2.77 1.21-1.49 2.83-3.44 4.08-3.44 1.63 0 1.65 1.01 1.76 1.79-3.78.64-5.38 3.67-5.38 5.37 0 1.7 1.44 3.09 3.21 3.09 1.63 0 4.29-1.33 4.69-6.1H21v-2.5h-2.47c-.15-1.65-1.09-4.2-4.03-4.2-2.25 0-4.18 1.91-4.94 2.84-.58.73-2.06 2.48-2.29 2.72-.25.3-.68.84-1.11.84-.45 0-.72-.83-.36-1.92.35-1.09 1.4-2.86 1.85-3.52.78-1.14 1.3-1.92 1.3-3.28C8.95 3.69 7.31 3 6.44 3 5.12 3 3.97 4 3.72 4.25c-.36.36-.66.66-.88.93l1.75 1.71zm9.29 11.66c-.31 0-.74-.26-.74-.72 0-.6.73-2.2 2.87-2.76-.3 2.69-1.43 3.48-2.13 3.48z"></path>
            </svg>
            <svg v-else-if="tool.key === 'polygon'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"></path>
            </svg>
          </div>
          <span>{{ tool.label }}</span>
        </div>
      </div>

      <!-- 清除工具部分 -->
      <div class="draw-tools-section">
        <div class="draw-tools-section-title">清除工具</div>
        <div
          v-for="tool in clearTools"
          :key="tool.key"
          class="draw-tools-item clear-item"
          @click="clearTool(tool)"
        >
          <div class="tool-icon">
            <svg v-if="tool.key === 'clearAll'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M15 16h4v2h-4zm0-8h7v2h-7zm0 4h6v2h-6zM3 18c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2V8H3v10zM14 5h-3l-1-1H6L5 5H2v2h12z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="currentColor">
              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"></path>
            </svg>
          </div>
          <span>{{ tool.label }}</span>
        </div>
      </div>
    </div>

    <!-- 引入绘图组件 -->
    <DrawPoint :map="map" v-if="map" ref="drawPointRef" class="hidden-tool" />
    <DrawLine :map="map" v-if="map" ref="drawLineRef" class="hidden-tool" />
    <DrawBezier :map="map" v-if="map" ref="drawBezierRef" class="hidden-tool" />
    <DrawPolygon :map="map" v-if="map" ref="drawPolygonRef" class="hidden-tool" />
  </div>
</template>

<style>
.draw-tools-container {
  position: absolute;
  top: 10px;
  left: 200px;
  z-index: 9000;
  font-size: 15px;
  color: #333;
  min-width: 120px;
}

.draw-tools-toggle {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  user-select: none;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  transition: background 0.2s;
}

.toggle-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.draw-tools-toggle:hover {
  background-color: #f5f7fa;
}

.triangle {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #1890ff;
  transition: transform 0.2s ease;
}

.triangle-up {
  transform: rotate(180deg);
}

.draw-tools-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 4px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  overflow: hidden;
  z-index: 5000;
  border: none;
}

.draw-tools-section-title {
  font-size: 14px;
  color: #1890ff;
  font-weight: 600;
  margin: 10px 0 6px 10px;
}

.draw-tools-item {
  padding: 10px 18px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  display: flex;
  align-items: center;
  font-size: 15px;
  border-radius: 0;
}

.draw-tools-item .tool-icon {
  margin-right: 10px;
  font-size: 18px;
}

.draw-tools-item:hover,
.draw-tools-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.clear-item {
  color: #ff4d4f;
}

.clear-item:hover {
  background-color: #e6f7ff;
  color: #ff4d4f;
}

.hidden-tool {
  display: none;
}

/* 导航栏中的绘制工具样式 */
.draw-tools-navbar {
  position: static !important;
}

.draw-tools-navbar .draw-tools-toggle {
  display: none !important;
}

.draw-tools-navbar .draw-tools-dropdown {
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: auto !important;
  right: 0 !important;
  border: 1px solid #e5e6eb !important;
  background-color: #fff !important;
  color: #222 !important;
  box-shadow: 0 6px 24px rgba(0,0,0,0.08) !important;
  animation: none !important;
  border-radius: 10px !important;
}

.draw-tools-navbar .draw-tools-section-title {
  color: #222 !important;
  background-color: #fff !important;
  font-weight: 500 !important;
}

.draw-tools-navbar .draw-tools-section {
  border-bottom: 1px solid #f0f0f0 !important;
}

.draw-tools-navbar .draw-tools-item {
  color: #222 !important;
  background: #fff !important;
  font-size: 15px !important;
}

.draw-tools-navbar .draw-tools-item:hover {
  background-color: #f5f7fa !important;
  color: #1890ff !important;
}

.draw-tools-navbar .draw-tools-item.active {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

.draw-tools-navbar .draw-tools-item.clear-item:hover {
  background-color: #fff1f0 !important;
  color: #ff4d4f !important;
}
</style>
