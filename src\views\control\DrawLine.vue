<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Draw from 'ol/interaction/Draw.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer.js'
import { Style, Circle as CircleStyle, Fill, Stroke, Text } from 'ol/style.js'
import Overlay from 'ol/Overlay.js'
import { LineString } from 'ol/geom.js'
import Feature from 'ol/Feature.js'
import { getLength } from 'ol/sphere.js'
import { unByKey } from 'ol/Observable.js'

const props = defineProps({
  map: Object
})

const drawing = ref(false)
const source = ref(null)
const vector = ref(null)
const draw = ref(null)
const lineCounter = ref(0)
const tooltipElement = ref(null)
const tooltip = ref(null)
const listener = ref(null)
const measureTooltipElement = ref(null)
const measureTooltip = ref(null)
const measureTooltips = ref([])
const measureTooltipElements = ref([])

// 初始化绘图工具
const initDraw = () => {
  if (!props.map) return

  // 创建矢量源和图层
  source.value = new VectorSource()
  vector.value = new VectorLayer({
    source: source.value,
    style: (feature) => {
      const lineName = feature.get('name') || ''

      return new Style({
        stroke: new Stroke({
          color: '#1890ff',
          width: 3
        }),
        text: new Text({
          text: lineName,
          placement: 'line',
          font: '14px sans-serif',
          fill: new Fill({
            color: '#333'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 3
          })
        })
      })
    },
    zIndex: 1000
  })

  props.map.addLayer(vector.value)

  createTooltip()
}

// 创建提示框
const createTooltip = () => {
  if (tooltipElement.value) {
    tooltipElement.value.parentNode.removeChild(tooltipElement.value)
  }

  tooltipElement.value = document.createElement('div')
  tooltipElement.value.className = 'ol-tooltip'
  tooltip.value = new Overlay({
    element: tooltipElement.value,
    offset: [15, 0],
    positioning: 'center-left'
  })

  props.map.addOverlay(tooltip.value)
}

// 创建测量提示框
const createMeasureTooltip = () => {
  const element = document.createElement('div')
  element.className = 'ol-tooltip ol-tooltip-measure'

  const tooltip = new Overlay({
    element: element,
    offset: [0, -15],
    positioning: 'bottom-center',
    stopEvent: false,
    insertFirst: false
  })

  props.map.addOverlay(tooltip)

  // 保存到列表中
  measureTooltipElements.value.push(element)
  measureTooltips.value.push(tooltip)

  // 设置当前使用的提示框
  measureTooltipElement.value = element
  measureTooltip.value = tooltip
}

// 格式化长度
const formatLength = (line) => {
  const length = getLength(line)
  let output

  if (length > 1000) {
    output = (Math.round(length / 1000 * 1000) / 1000) + ' km'
  } else {
    output = (Math.round(length * 100) / 100) + ' m'
  }

  return output
}

// 开始绘制线
const startDrawLine = () => {
  if (!props.map) return

  if (drawing.value) {
    stopDrawing()
  }

  drawing.value = true

  createMeasureTooltip()

  // 创建绘图交互
  draw.value = new Draw({
    source: source.value,
    type: 'LineString',
    style: new Style({
      stroke: new Stroke({
        color: '#1890ff',
        lineDash: [6, 6],
        width: 2
      }),
      image: new CircleStyle({
        radius: 5,
        stroke: new Stroke({
          color: '#1890ff',
          width: 1.5
        }),
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.8)'
        })
      })
    })
  })

  props.map.addInteraction(draw.value)

  // 添加绘图开始事件
  draw.value.on('drawstart', (evt) => {
    createMeasureTooltip()

    const sketch = evt.feature

    let tooltipCoord = null

    // 添加几何变化监听
    listener.value = sketch.getGeometry().on('change', (e) => {
      const geom = e.target
      const output = formatLength(geom)
      tooltipCoord = geom.getLastCoordinate()

      // 显示每段距离
      const coordinates = geom.getCoordinates()
      if (coordinates.length > 1) {
        // 清除之前的线段标记
        measureTooltips.value.forEach(tooltip => {
          if (tooltip.getElement().className.includes('ol-tooltip-segment')) {
            props.map.removeOverlay(tooltip)
            const index = measureTooltips.value.indexOf(tooltip)
            if (index > -1) {
              measureTooltips.value.splice(index, 1)
              measureTooltipElements.value.splice(index, 1)
            }
          }
        })

        // 为每段线添加距离标记
        for (let i = 1; i < coordinates.length; i++) {
          const segment = new LineString([coordinates[i-1], coordinates[i]])
          const segmentText = formatLength(segment)

          // 创建线段测量提示框
          const segElement = document.createElement('div')
          segElement.className = 'ol-tooltip ol-tooltip-segment'
          segElement.innerHTML = segmentText

          const segTooltip = new Overlay({
            element: segElement,
            offset: [0, -15],
            positioning: 'bottom-center',
            stopEvent: false,
            insertFirst: false
          })

          // 计算线段中点
          const midpoint = [
            (coordinates[i-1][0] + coordinates[i][0]) / 2,
            (coordinates[i-1][1] + coordinates[i][1]) / 2
          ]

          segTooltip.setPosition(midpoint)
          props.map.addOverlay(segTooltip)

          // 保存到列表中
          measureTooltipElements.value.push(segElement)
          measureTooltips.value.push(segTooltip)
        }
      }

      // 更新总长度提示框
      measureTooltipElement.value.innerHTML = `总长度: ${output}`
      measureTooltip.value.setPosition(tooltipCoord)
    })
  })

  // 添加绘图结束事件
  draw.value.on('drawend', (evt) => {
    const feature = evt.feature
    const geometry = feature.getGeometry()

    lineCounter.value++

    feature.set('name', `线 ${lineCounter.value}`)

    const length = getLength(geometry)
    const formattedLength = formatLength(geometry)

    measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-static'
    measureTooltip.value.setOffset([0, -7])

    measureTooltipElement.value = null

    unByKey(listener.value)
    listener.value = null

    console.log(`线 ${lineCounter.value}: ${formattedLength} (${length} 米)`)

    stopDrawing()
  })

  // 添加鼠标移动事件
  const pointerMoveHandler = (evt) => {
    if (evt.dragging || !drawing.value) {
      return
    }

    let helpMsg = '点击地图开始绘制线'

    if (draw.value && draw.value.sketchFeature_) {
      const sketchGeom = draw.value.sketchFeature_.getGeometry()
      const coords = sketchGeom.getCoordinates()

      if (coords.length > 1) {
        helpMsg = '点击继续绘制，双击结束'
      } else {
        helpMsg = '点击添加下一个点'
      }
    }

    if (tooltipElement.value) {
      tooltipElement.value.innerHTML = helpMsg
      tooltip.value.setPosition(evt.coordinate)
    }
  }

  props.map.on('pointermove', pointerMoveHandler)
}

// 停止绘制（但保留已绘制的线）
const stopDrawing = () => {
  if (!props.map) return

  drawing.value = false

  // 移除绘图交互
  if (draw.value) {
    props.map.removeInteraction(draw.value)
    draw.value = null
  }

  // 移除动态提示框（但保留静态标签）
  if (tooltip.value) {
    props.map.removeOverlay(tooltip.value)
    tooltip.value = null
  }

  if (tooltipElement.value) {
    if (tooltipElement.value.parentNode) {
      tooltipElement.value.parentNode.removeChild(tooltipElement.value)
    }
    tooltipElement.value = null
  }

  // 移除监听器
  if (listener.value) {
    unByKey(listener.value)
    listener.value = null
  }

  // 只移除临时测量提示框，保留静态标签
  measureTooltips.value.forEach(tooltip => {
    if (tooltip.getElement().className.includes('ol-tooltip-measure') &&
        !tooltip.getElement().className.includes('ol-tooltip-static')) {
      props.map.removeOverlay(tooltip)
      const index = measureTooltips.value.indexOf(tooltip)
      if (index > -1) {
        measureTooltips.value.splice(index, 1)
        measureTooltipElements.value.splice(index, 1)
      }
    }
  })

  createTooltip()

  // 注意：不清除 source 中的要素，保留已绘制的线和静态标签
}

// 清除所有线
const clearLines = () => {
  if (!props.map) return

  stopDrawing()

  if (source.value) {
    source.value.clear()
  }

  // 移除所有覆盖物
  const overlays = props.map.getOverlays().getArray().slice()
  overlays.forEach(overlay => {
    const element = overlay.getElement()
    if (element && element.className &&
        (element.className.includes('ol-tooltip-static') ||
         element.className.includes('ol-tooltip-segment'))) {
      props.map.removeOverlay(overlay)
    }
  })

  // 清空测量提示框列表
  measureTooltips.value.forEach(tooltip => {
    props.map.removeOverlay(tooltip)
  })

  measureTooltipElements.value.forEach(element => {
    if (element.parentNode) {
      element.parentNode.removeChild(element)
    }
  })

  measureTooltips.value = []
  measureTooltipElements.value = []
  measureTooltip.value = null
  measureTooltipElement.value = null

  // 重置
  lineCounter.value = 0

  createTooltip()

  const successMsg = document.createElement('div')
  successMsg.className = 'ol-tooltip ol-tooltip-success'
  successMsg.innerHTML = '已清除所有线'

  const successTooltip = new Overlay({
    element: successMsg,
    offset: [0, 0],
    positioning: 'center-center',
    stopEvent: false
  })

  // 获取地图中心点
  const center = props.map.getView().getCenter()
  successTooltip.setPosition(center)
  props.map.addOverlay(successTooltip)

  // 2秒后自动移除提示
  setTimeout(() => {
    props.map.removeOverlay(successTooltip)
    if (successMsg.parentNode) {
      successMsg.parentNode.removeChild(successMsg)
    }
  }, 1500)
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initDraw()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  stopDrawing()

  if (props.map && vector.value) {
    props.map.removeLayer(vector.value)
  }
})

defineExpose({
  startDrawLine,
  stopDrawing,
  clearLines
})
</script>

<template>

</template>

<style>
.draw-tools {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.draw-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.draw-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 16px;
}

.draw-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.draw-button.active {
  background-color: #1890ff;
  color: white;
}

.draw-button.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.draw-button.clear-button:hover {
  background-color: #ff4d4f;
  color: white;
}

/* 提示框样式 */
.ol-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #333;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
}

.ol-tooltip-static {
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-segment {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid #1890ff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.ol-tooltip-success {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 1.5s ease-in-out;
  pointer-events: none;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.8); }
  20% { opacity: 1; transform: scale(1.1); }
  30% { opacity: 1; transform: scale(1); }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
</style>
